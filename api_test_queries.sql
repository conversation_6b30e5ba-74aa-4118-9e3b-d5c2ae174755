-- 蓝牙桌牌系统API对应的SQL查询测试
-- 基于API文档设计的数据库查询语句

-- ================================
-- Authentication 模块测试查询
-- ================================

-- 1. 用户登录验证 (POST /auth/login)
-- 验证用户名和密码，返回用户信息
SELECT id, username, email, status 
FROM users 
WHERE username = '20250529002' AND status = 'active';

-- 2. 创建访问令牌
INSERT INTO access_tokens (user_id, token, expires_at) 
VALUES (2, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...', DATE_ADD(NOW(), INTERVAL 24 HOUR));

-- 3. 验证访问令牌
SELECT u.id, u.username, u.email, u.status
FROM users u
JOIN access_tokens t ON u.id = t.user_id
WHERE t.token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' 
  AND t.expires_at > NOW();

-- ================================
-- DEVICE 模块测试查询
-- ================================

-- 4. 查询所有设备 (GET /devices)
-- 支持过滤条件
SELECT 
    d.id,
    d.device_id,
    d.device_name,
    d.mac_address,
    d.device_type,
    d.status,
    d.battery_level,
    d.firmware_version,
    ap.ap_name as gateway_name,
    ap.location as gateway_location
FROM devices d
LEFT JOIN access_points ap ON d.ap_id = ap.id
WHERE d.status IN ('online', 'offline')
ORDER BY d.created_at DESC;

-- 5. 查询单个设备 (GET /device/{id})
SELECT 
    d.*,
    ap.ap_name,
    ap.mac_address as gateway_mac,
    db.binding_type,
    db.binding_data,
    ms.staff_name,
    ms.position,
    t.template_name,
    mr.room_name
FROM devices d
LEFT JOIN access_points ap ON d.ap_id = ap.id
LEFT JOIN device_bindings db ON d.id = db.device_id
LEFT JOIN meeting_staff ms ON db.staff_id = ms.id
LEFT JOIN templates t ON db.template_id = t.id
LEFT JOIN meeting_rooms mr ON db.meeting_room_id = mr.id
WHERE d.device_id = 'DEV001';

-- 6. 绑定设备到人员和模板 (POST /device/bind)
-- 首先检查设备、人员、模板是否存在
SELECT 
    d.id as device_id,
    ms.id as staff_id,
    t.id as template_id,
    mr.id as room_id
FROM devices d
CROSS JOIN meeting_staff ms
CROSS JOIN templates t
CROSS JOIN meeting_rooms mr
WHERE d.device_id = 'DEV001' 
  AND ms.staff_code = 'EMP001'
  AND t.id = 1
  AND mr.room_code = 'ROOM_A';

-- 执行绑定
INSERT INTO device_bindings (device_id, staff_id, template_id, meeting_room_id, binding_type, binding_data)
VALUES (1, 1, 1, 1, 'staff', '{"display_name":"张三","position":"项目经理","department":"技术部","last_updated":"2025-01-01 10:00:00"}');

-- 7. 批量绑定设备 (POST /device/bind-multiple)
INSERT INTO device_bindings (device_id, staff_id, template_id, meeting_room_id, binding_type, binding_data)
VALUES 
(2, 2, 1, 1, 'staff', '{"display_name":"李四","position":"高级工程师","department":"技术部"}'),
(3, 3, 1, 2, 'staff', '{"display_name":"王五","position":"产品经理","department":"产品部"}');

-- 8. 更新设备信息 (POST /device/update)
UPDATE devices 
SET device_name = '桌牌-001-更新', 
    battery_level = 80,
    status = 'online',
    updated_at = NOW()
WHERE device_id = 'DEV001';

-- ================================
-- MEETING 模块测试查询
-- ================================

-- 9. 查询会议室列表 (GET /meeting/rooms)
SELECT 
    mr.*,
    COUNT(ms.id) as staff_count,
    COUNT(db.id) as device_count
FROM meeting_rooms mr
LEFT JOIN meeting_staff ms ON mr.id = ms.meeting_room_id AND ms.status = 'active'
LEFT JOIN device_bindings db ON mr.id = db.meeting_room_id
WHERE mr.status = 'available'
GROUP BY mr.id
ORDER BY mr.room_name;

-- 10. 创建会议室 (POST /meeting/room)
INSERT INTO meeting_rooms (room_name, room_code, location, capacity, equipment, status)
VALUES ('新会议室', 'ROOM_NEW', '4楼北侧', 12, '{"projector": true, "whiteboard": true, "video_conference": true}', 'available');

-- 11. 查询人员列表 (GET /meeting/staff)
SELECT 
    ms.*,
    mr.room_name,
    mr.room_code,
    COUNT(db.id) as device_count
FROM meeting_staff ms
LEFT JOIN meeting_rooms mr ON ms.meeting_room_id = mr.id
LEFT JOIN device_bindings db ON ms.id = db.staff_id
WHERE ms.status = 'active'
GROUP BY ms.id
ORDER BY ms.staff_name;

-- 12. 创建会议人员 (POST /meeting/staff)
INSERT INTO meeting_staff (staff_name, staff_code, position, department, email, phone, meeting_room_id, status)
VALUES ('新员工', 'EMP005', '软件工程师', '技术部', '<EMAIL>', '13800138005', 1, 'active');

-- 13. 批量创建会议人员 (POST /meeting/staff/batch)
INSERT INTO meeting_staff (staff_name, staff_code, position, department, email, phone, meeting_room_id, status)
VALUES 
('员工A', 'EMP006', '测试工程师', '质量部', '<EMAIL>', '13800138006', 2, 'active'),
('员工B', 'EMP007', '运维工程师', '运维部', '<EMAIL>', '13800138007', 2, 'active');

-- 14. 更新会议人员 (POST /meeting/staff/update)
UPDATE meeting_staff 
SET position = '高级项目经理',
    department = '技术部',
    email = '<EMAIL>',
    updated_at = NOW()
WHERE staff_code = 'EMP001';

-- 15. 删除会议人员 (GET /meeting/staff/delete)
-- 先检查是否有关联的设备绑定
SELECT COUNT(*) as binding_count 
FROM device_bindings 
WHERE staff_id = (SELECT id FROM meeting_staff WHERE staff_code = 'EMP007');

-- 如果没有绑定，则可以删除
UPDATE meeting_staff 
SET status = 'inactive', updated_at = NOW()
WHERE staff_code = 'EMP007';

-- ================================
-- TEMPLATE 模块测试查询
-- ================================

-- 16. 查询模板列表 (GET /templates)
SELECT 
    t.*,
    u.username as creator_name,
    COUNT(db.id) as usage_count
FROM templates t
LEFT JOIN users u ON t.created_by = u.id
LEFT JOIN device_bindings db ON t.id = db.template_id
WHERE t.status = 'active'
GROUP BY t.id
ORDER BY t.created_at DESC;

-- 17. 创建模板 (POST /template)
INSERT INTO templates (template_name, template_type, design_data, width, height, is_default, status, created_by)
VALUES (
    '新桌牌模板', 
    'nameplate', 
    '{"background":"#f8f9fa","fields":[{"type":"text","name":"staff_name","x":15,"y":15,"width":200,"height":35,"fontSize":18,"color":"#212529","fontWeight":"bold"},{"type":"text","name":"position","x":15,"y":55,"width":200,"height":25,"fontSize":14,"color":"#6c757d"}]}',
    296, 
    128, 
    FALSE, 
    'active', 
    1
);

-- 18. 获取模板设计数据 (GET /template/design)
SELECT 
    id,
    template_name,
    template_type,
    design_data,
    width,
    height,
    preview_image
FROM templates 
WHERE id = 1 AND status = 'active';

-- 19. 更新模板 (PUT /template)
UPDATE templates 
SET template_name = '标准桌牌模板-v2',
    design_data = '{"background":"#ffffff","fields":[{"type":"text","name":"staff_name","x":10,"y":10,"width":200,"height":30,"fontSize":16,"color":"#000000","fontWeight":"normal"},{"type":"text","name":"position","x":10,"y":45,"width":200,"height":20,"fontSize":12,"color":"#666666"}]}',
    updated_at = NOW()
WHERE id = 1;

-- 20. 删除模板 (DELETE /template)
-- 先检查是否有设备在使用
SELECT COUNT(*) as usage_count 
FROM device_bindings 
WHERE template_id = 3;

-- 如果没有使用，则可以删除
UPDATE templates 
SET status = 'inactive', updated_at = NOW()
WHERE id = 3;

-- ================================
-- AP 模块测试查询
-- ================================

-- 21. 获取网关列表 (GET /ap/list)
SELECT 
    ap.*,
    COUNT(d.id) as connected_devices
FROM access_points ap
LEFT JOIN devices d ON ap.id = d.ap_id AND d.status IN ('online', 'binding')
GROUP BY ap.id
ORDER BY ap.ap_name;

-- 22. 创建网关 (POST /ap)
INSERT INTO access_points (ap_name, mac_address, ip_address, location, status, firmware_version, max_devices)
VALUES ('网关-4楼研发区', '00:11:22:33:44:58', '*************', '4楼研发区', 'offline', 'v1.2.3', 60);

-- 23. 根据MAC获取网关信息 (GET /ap/{mac})
SELECT 
    ap.*,
    COUNT(d.id) as connected_devices,
    GROUP_CONCAT(d.device_id) as device_list
FROM access_points ap
LEFT JOIN devices d ON ap.id = d.ap_id
WHERE ap.mac_address = '00:11:22:33:44:55'
GROUP BY ap.id;

-- 24. 修改网关信息 (PUT /ap)
UPDATE access_points 
SET ap_name = '网关-1楼大厅-主网关',
    location = '1楼大厅中央',
    max_devices = 80,
    updated_at = NOW()
WHERE mac_address = '00:11:22:33:44:55';

-- 25. 删除网关 (DELETE /ap)
-- 先检查是否有连接的设备
SELECT COUNT(*) as connected_devices 
FROM devices 
WHERE ap_id = (SELECT id FROM access_points WHERE mac_address = '00:11:22:33:44:58');

-- 如果没有连接设备，则可以删除
DELETE FROM access_points 
WHERE mac_address = '00:11:22:33:44:58' 
  AND id NOT IN (SELECT DISTINCT ap_id FROM devices WHERE ap_id IS NOT NULL);

-- ================================
-- 综合查询示例
-- ================================

-- 26. 获取设备完整状态信息
SELECT 
    d.device_id,
    d.device_name,
    d.status as device_status,
    d.battery_level,
    ap.ap_name,
    ap.status as gateway_status,
    ms.staff_name,
    ms.position,
    mr.room_name,
    t.template_name,
    db.binding_data,
    db.created_at as binding_time
FROM devices d
LEFT JOIN access_points ap ON d.ap_id = ap.id
LEFT JOIN device_bindings db ON d.id = db.device_id
LEFT JOIN meeting_staff ms ON db.staff_id = ms.id
LEFT JOIN meeting_rooms mr ON db.meeting_room_id = mr.id
LEFT JOIN templates t ON db.template_id = t.id
ORDER BY d.device_id;

-- 27. 统计报表查询
SELECT 
    '设备总数' as metric,
    COUNT(*) as value
FROM devices
UNION ALL
SELECT 
    '在线设备数',
    COUNT(*)
FROM devices 
WHERE status = 'online'
UNION ALL
SELECT 
    '网关总数',
    COUNT(*)
FROM access_points
UNION ALL
SELECT 
    '活跃人员数',
    COUNT(*)
FROM meeting_staff 
WHERE status = 'active'
UNION ALL
SELECT 
    '可用会议室数',
    COUNT(*)
FROM meeting_rooms 
WHERE status = 'available';
