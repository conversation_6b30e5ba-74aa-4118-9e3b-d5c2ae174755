-- 蓝牙桌牌系统数据库创建脚本
-- 基于API文档分析设计

-- 创建数据库
CREATE DATABASE IF NOT EXISTS bluetooth_nameplate_system 
CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE bluetooth_nameplate_system;

-- 1. 用户表
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码(加密存储)',
    email VARCHAR(100) COMMENT '邮箱',
    status ENUM('active', 'inactive') DEFAULT 'active' COMMENT '状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT '用户表';

-- 2. 访问令牌表
CREATE TABLE access_tokens (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    token VARCHAR(500) NOT NULL COMMENT 'JWT令牌',
    expires_at TIMESTAMP NOT NULL COMMENT '过期时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) COMMENT '访问令牌表';

-- 3. 网关/接入点表
CREATE TABLE access_points (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    ap_name VARCHAR(200) NOT NULL COMMENT '网关名称',
    mac_address VARCHAR(17) UNIQUE NOT NULL COMMENT 'MAC地址',
    ip_address VARCHAR(15) COMMENT 'IP地址',
    location VARCHAR(300) COMMENT '位置',
    status ENUM('online', 'offline', 'error') DEFAULT 'offline' COMMENT '状态',
    firmware_version VARCHAR(50) COMMENT '固件版本',
    signal_strength INT DEFAULT 0 COMMENT '信号强度',
    connected_devices_count INT DEFAULT 0 COMMENT '已连接设备数',
    max_devices INT DEFAULT 50 COMMENT '最大设备数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT '网关/接入点表';

-- 4. 设备表
CREATE TABLE devices (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    device_id VARCHAR(100) UNIQUE NOT NULL COMMENT '设备ID',
    device_name VARCHAR(200) COMMENT '设备名称',
    mac_address VARCHAR(17) UNIQUE COMMENT 'MAC地址',
    device_type ENUM('nameplate', 'price_tag') DEFAULT 'nameplate' COMMENT '设备类型',
    status ENUM('online', 'offline', 'binding', 'error') DEFAULT 'offline' COMMENT '设备状态',
    battery_level INT DEFAULT 100 COMMENT '电池电量',
    firmware_version VARCHAR(50) COMMENT '固件版本',
    ap_id BIGINT COMMENT '所属网关ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (ap_id) REFERENCES access_points(id) ON DELETE SET NULL
) COMMENT '设备表';

-- 5. 会议室表
CREATE TABLE meeting_rooms (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    room_name VARCHAR(200) NOT NULL COMMENT '会议室名称',
    room_code VARCHAR(50) UNIQUE COMMENT '会议室编码',
    location VARCHAR(300) COMMENT '位置',
    capacity INT DEFAULT 0 COMMENT '容量',
    equipment JSON COMMENT '设备信息',
    status ENUM('available', 'occupied', 'maintenance') DEFAULT 'available' COMMENT '状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT '会议室表';

-- 6. 会议人员表
CREATE TABLE meeting_staff (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    staff_name VARCHAR(100) NOT NULL COMMENT '人员姓名',
    staff_code VARCHAR(50) UNIQUE COMMENT '人员编码',
    position VARCHAR(100) COMMENT '职位',
    department VARCHAR(100) COMMENT '部门',
    email VARCHAR(100) COMMENT '邮箱',
    phone VARCHAR(20) COMMENT '电话',
    avatar_url VARCHAR(500) COMMENT '头像URL',
    meeting_room_id BIGINT COMMENT '所属会议室ID',
    status ENUM('active', 'inactive') DEFAULT 'active' COMMENT '状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (meeting_room_id) REFERENCES meeting_rooms(id) ON DELETE SET NULL
) COMMENT '会议人员表';

-- 7. 模板表
CREATE TABLE templates (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    template_name VARCHAR(200) NOT NULL COMMENT '模板名称',
    template_type ENUM('nameplate', 'meeting', 'custom') DEFAULT 'nameplate' COMMENT '模板类型',
    design_data JSON NOT NULL COMMENT '设计数据',
    preview_image VARCHAR(500) COMMENT '预览图片URL',
    width INT DEFAULT 296 COMMENT '宽度(像素)',
    height INT DEFAULT 128 COMMENT '高度(像素)',
    is_default BOOLEAN DEFAULT FALSE COMMENT '是否默认模板',
    status ENUM('active', 'inactive') DEFAULT 'active' COMMENT '状态',
    created_by BIGINT COMMENT '创建者ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
) COMMENT '模板表';

-- 8. 设备绑定表
CREATE TABLE device_bindings (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    device_id BIGINT NOT NULL COMMENT '设备ID',
    staff_id BIGINT COMMENT '人员ID',
    template_id BIGINT COMMENT '模板ID',
    meeting_room_id BIGINT COMMENT '会议室ID',
    binding_type ENUM('staff', 'room', 'template') NOT NULL COMMENT '绑定类型',
    binding_data JSON COMMENT '绑定数据',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (device_id) REFERENCES devices(id) ON DELETE CASCADE,
    FOREIGN KEY (staff_id) REFERENCES meeting_staff(id) ON DELETE CASCADE,
    FOREIGN KEY (template_id) REFERENCES templates(id) ON DELETE CASCADE,
    FOREIGN KEY (meeting_room_id) REFERENCES meeting_rooms(id) ON DELETE CASCADE
) COMMENT '设备绑定表';

-- 创建索引
-- 设备表索引
CREATE INDEX idx_devices_mac ON devices(mac_address);
CREATE INDEX idx_devices_status ON devices(status);
CREATE INDEX idx_devices_ap_id ON devices(ap_id);
CREATE INDEX idx_devices_type ON devices(device_type);

-- 设备绑定表索引
CREATE INDEX idx_bindings_device_id ON device_bindings(device_id);
CREATE INDEX idx_bindings_staff_id ON device_bindings(staff_id);
CREATE INDEX idx_bindings_template_id ON device_bindings(template_id);
CREATE INDEX idx_bindings_type ON device_bindings(binding_type);
CREATE INDEX idx_bindings_room_id ON device_bindings(meeting_room_id);

-- 会议人员表索引
CREATE INDEX idx_staff_room_id ON meeting_staff(meeting_room_id);
CREATE INDEX idx_staff_status ON meeting_staff(status);
CREATE INDEX idx_staff_code ON meeting_staff(staff_code);

-- 网关表索引
CREATE INDEX idx_ap_mac ON access_points(mac_address);
CREATE INDEX idx_ap_status ON access_points(status);

-- 模板表索引
CREATE INDEX idx_templates_type ON templates(template_type);
CREATE INDEX idx_templates_status ON templates(status);
CREATE INDEX idx_templates_created_by ON templates(created_by);

-- 会议室表索引
CREATE INDEX idx_rooms_code ON meeting_rooms(room_code);
CREATE INDEX idx_rooms_status ON meeting_rooms(status);

-- 令牌表索引
CREATE INDEX idx_tokens_user_id ON access_tokens(user_id);
CREATE INDEX idx_tokens_expires ON access_tokens(expires_at);

-- 用户表索引
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_status ON users(status);

-- 创建触发器
DELIMITER //

-- 更新网关连接设备数量的触发器
CREATE TRIGGER update_ap_device_count_insert
AFTER INSERT ON devices
FOR EACH ROW
BEGIN
    IF NEW.ap_id IS NOT NULL THEN
        UPDATE access_points
        SET connected_devices_count = (
            SELECT COUNT(*) FROM devices WHERE ap_id = NEW.ap_id
        )
        WHERE id = NEW.ap_id;
    END IF;
END//

CREATE TRIGGER update_ap_device_count_update
AFTER UPDATE ON devices
FOR EACH ROW
BEGIN
    -- 如果网关发生变化
    IF OLD.ap_id != NEW.ap_id THEN
        -- 更新旧网关的设备数量
        IF OLD.ap_id IS NOT NULL THEN
            UPDATE access_points
            SET connected_devices_count = (
                SELECT COUNT(*) FROM devices WHERE ap_id = OLD.ap_id
            )
            WHERE id = OLD.ap_id;
        END IF;

        -- 更新新网关的设备数量
        IF NEW.ap_id IS NOT NULL THEN
            UPDATE access_points
            SET connected_devices_count = (
                SELECT COUNT(*) FROM devices WHERE ap_id = NEW.ap_id
            )
            WHERE id = NEW.ap_id;
        END IF;
    END IF;
END//

CREATE TRIGGER update_ap_device_count_delete
AFTER DELETE ON devices
FOR EACH ROW
BEGIN
    IF OLD.ap_id IS NOT NULL THEN
        UPDATE access_points
        SET connected_devices_count = (
            SELECT COUNT(*) FROM devices WHERE ap_id = OLD.ap_id
        )
        WHERE id = OLD.ap_id;
    END IF;
END//

DELIMITER ;

-- 插入示例数据
-- 插入管理员用户
INSERT INTO users (username, password, email, status) VALUES
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', 'active'),
('20250529002', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', 'active');

-- 插入网关示例数据
INSERT INTO access_points (ap_name, mac_address, ip_address, location, status, firmware_version) VALUES
('网关-1楼大厅', '00:11:22:33:44:55', '*************', '1楼大厅', 'online', 'v1.2.3'),
('网关-2楼会议区', '00:11:22:33:44:56', '*************', '2楼会议区', 'online', 'v1.2.3'),
('网关-3楼办公区', '00:11:22:33:44:57', '*************', '3楼办公区', 'offline', 'v1.2.2');

-- 插入会议室示例数据
INSERT INTO meeting_rooms (room_name, room_code, location, capacity, status) VALUES
('会议室A', 'ROOM_A', '2楼东侧', 10, 'available'),
('会议室B', 'ROOM_B', '2楼西侧', 8, 'available'),
('大会议室', 'ROOM_MAIN', '3楼中央', 50, 'available'),
('小会议室1', 'ROOM_S1', '2楼南侧', 4, 'available');

-- 插入会议人员示例数据
INSERT INTO meeting_staff (staff_name, staff_code, position, department, email, phone, meeting_room_id, status) VALUES
('张三', 'EMP001', '项目经理', '技术部', '<EMAIL>', '13800138001', 1, 'active'),
('李四', 'EMP002', '高级工程师', '技术部', '<EMAIL>', '13800138002', 1, 'active'),
('王五', 'EMP003', '产品经理', '产品部', '<EMAIL>', '13800138003', 2, 'active'),
('赵六', 'EMP004', 'UI设计师', '设计部', '<EMAIL>', '13800138004', 2, 'active');

-- 插入模板示例数据
INSERT INTO templates (template_name, template_type, design_data, width, height, is_default, status, created_by) VALUES
('标准桌牌模板', 'nameplate', '{"background":"#ffffff","fields":[{"type":"text","name":"staff_name","x":10,"y":10,"width":200,"height":30,"fontSize":16,"color":"#000000"}]}', 296, 128, TRUE, 'active', 1),
('会议室模板', 'meeting', '{"background":"#f0f0f0","fields":[{"type":"text","name":"room_name","x":10,"y":10,"width":250,"height":40,"fontSize":20,"color":"#333333"}]}', 296, 128, FALSE, 'active', 1),
('简约模板', 'custom', '{"background":"#ffffff","fields":[{"type":"text","name":"staff_name","x":20,"y":20,"width":180,"height":25,"fontSize":14,"color":"#666666"}]}', 296, 128, FALSE, 'active', 1);

-- 插入设备示例数据
INSERT INTO devices (device_id, device_name, mac_address, device_type, status, battery_level, firmware_version, ap_id) VALUES
('DEV001', '桌牌-001', 'AA:BB:CC:DD:EE:01', 'nameplate', 'online', 85, 'v2.1.0', 1),
('DEV002', '桌牌-002', 'AA:BB:CC:DD:EE:02', 'nameplate', 'online', 92, 'v2.1.0', 1),
('DEV003', '桌牌-003', 'AA:BB:CC:DD:EE:03', 'nameplate', 'offline', 78, 'v2.0.5', 2),
('DEV004', '桌牌-004', 'AA:BB:CC:DD:EE:04', 'nameplate', 'online', 95, 'v2.1.0', 2);

-- 插入设备绑定示例数据
INSERT INTO device_bindings (device_id, staff_id, template_id, meeting_room_id, binding_type, binding_data) VALUES
(1, 1, 1, 1, 'staff', '{"display_name":"张三","position":"项目经理","department":"技术部"}'),
(2, 2, 1, 1, 'staff', '{"display_name":"李四","position":"高级工程师","department":"技术部"}'),
(3, 3, 1, 2, 'staff', '{"display_name":"王五","position":"产品经理","department":"产品部"}'),
(4, 4, 1, 2, 'staff', '{"display_name":"赵六","position":"UI设计师","department":"设计部"}');
