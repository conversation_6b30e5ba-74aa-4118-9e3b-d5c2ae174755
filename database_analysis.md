# 蓝牙桌牌系统数据库分析

## 系统概述
基于Apifox API文档分析，这是一个蓝牙桌牌管理系统，主要用于会议室场景的电子桌牌管理。系统包含以下核心模块：

- **Authentication**: 用户认证和令牌管理
- **DEVICE**: 蓝牙设备管理（桌牌绑定、刷新、状态查询）
- **MEETING**: 会议室和人员管理
- **TEMPLATE**: 桌牌显示模板管理
- **AP**: 蓝牙网关/接入点管理

## API端点分析
根据文档，系统提供以下主要功能：
1. 设备绑定到人员和模板
2. 桌牌内容刷新和闪烁控制
3. 会议室和人员的CRUD操作
4. 模板的创建、更新、删除和设计管理
5. 网关设备的管理和监控

## 核心业务实体分析

### 1. 用户认证模块 (Authentication)
**核心表：users**
```sql
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    status ENUM('active', 'inactive') DEFAULT 'active'
);
```

**相关表：access_tokens**
```sql
CREATE TABLE access_tokens (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    token VARCHAR(500) NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

### 2. 设备管理模块 (DEVICE)
**核心表：devices (蓝牙桌牌设备)**
```sql
CREATE TABLE devices (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    device_id VARCHAR(100) UNIQUE NOT NULL,
    device_name VARCHAR(200),
    mac_address VARCHAR(17) UNIQUE,
    device_type ENUM('nameplate', 'price_tag') DEFAULT 'nameplate',
    status ENUM('online', 'offline', 'binding', 'error') DEFAULT 'offline',
    battery_level INT DEFAULT 100,
    firmware_version VARCHAR(50),
    ap_id BIGINT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (ap_id) REFERENCES access_points(id) ON DELETE SET NULL
);
```

**设备绑定表：device_bindings**
```sql
CREATE TABLE device_bindings (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    device_id BIGINT NOT NULL,
    staff_id BIGINT,
    template_id BIGINT,
    meeting_room_id BIGINT,
    binding_type ENUM('staff', 'room', 'template') NOT NULL,
    binding_data JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (device_id) REFERENCES devices(id) ON DELETE CASCADE,
    FOREIGN KEY (staff_id) REFERENCES meeting_staff(id) ON DELETE CASCADE,
    FOREIGN KEY (template_id) REFERENCES templates(id) ON DELETE CASCADE,
    FOREIGN KEY (meeting_room_id) REFERENCES meeting_rooms(id) ON DELETE CASCADE
);
```

### 3. 会议管理模块 (MEETING)
**会议室表：meeting_rooms**
```sql
CREATE TABLE meeting_rooms (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    room_name VARCHAR(200) NOT NULL,
    room_code VARCHAR(50) UNIQUE,
    location VARCHAR(300),
    capacity INT DEFAULT 0,
    equipment JSON,
    status ENUM('available', 'occupied', 'maintenance') DEFAULT 'available',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

**会议人员表：meeting_staff**
```sql
CREATE TABLE meeting_staff (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    staff_name VARCHAR(100) NOT NULL,
    staff_code VARCHAR(50) UNIQUE,
    position VARCHAR(100),
    department VARCHAR(100),
    email VARCHAR(100),
    phone VARCHAR(20),
    avatar_url VARCHAR(500),
    meeting_room_id BIGINT,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (meeting_room_id) REFERENCES meeting_rooms(id) ON DELETE SET NULL
);
```

### 4. 模板管理模块 (TEMPLATE)
**模板表：templates**
```sql
CREATE TABLE templates (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    template_name VARCHAR(200) NOT NULL,
    template_type ENUM('nameplate', 'meeting', 'custom') DEFAULT 'nameplate',
    design_data JSON NOT NULL,
    preview_image VARCHAR(500),
    width INT DEFAULT 296,
    height INT DEFAULT 128,
    is_default BOOLEAN DEFAULT FALSE,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_by BIGINT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);
```

### 5. 网关管理模块 (AP)
**接入点/网关表：access_points**
```sql
CREATE TABLE access_points (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    ap_name VARCHAR(200) NOT NULL,
    mac_address VARCHAR(17) UNIQUE NOT NULL,
    ip_address VARCHAR(15),
    location VARCHAR(300),
    status ENUM('online', 'offline', 'error') DEFAULT 'offline',
    firmware_version VARCHAR(50),
    signal_strength INT DEFAULT 0,
    connected_devices_count INT DEFAULT 0,
    max_devices INT DEFAULT 50,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

## 关系图分析

### 核心关系
1. **用户 → 访问令牌**: 一对多关系，用户可以有多个有效令牌
2. **设备 → 网关**: 多对一关系，多个设备连接到一个网关
3. **设备 → 绑定**: 一对多关系，一个设备可以有多个绑定记录（历史记录）
4. **会议室 → 人员**: 一对多关系，一个会议室可以有多个人员
5. **模板 → 设备绑定**: 一对多关系，一个模板可以被多个设备使用
6. **用户 → 模板**: 一对多关系，用户可以创建多个模板

### 业务流程关系
1. **设备绑定流程**: 设备 → 绑定表 → (人员/模板/会议室)
2. **模板应用流程**: 模板 → 设备绑定 → 设备显示
3. **网关管理流程**: 网关 → 设备连接 → 状态监控

## 索引建议

```sql
-- 设备表索引
CREATE INDEX idx_devices_mac ON devices(mac_address);
CREATE INDEX idx_devices_status ON devices(status);
CREATE INDEX idx_devices_ap_id ON devices(ap_id);

-- 设备绑定表索引
CREATE INDEX idx_bindings_device_id ON device_bindings(device_id);
CREATE INDEX idx_bindings_staff_id ON device_bindings(staff_id);
CREATE INDEX idx_bindings_template_id ON device_bindings(template_id);
CREATE INDEX idx_bindings_type ON device_bindings(binding_type);

-- 会议人员表索引
CREATE INDEX idx_staff_room_id ON meeting_staff(meeting_room_id);
CREATE INDEX idx_staff_status ON meeting_staff(status);

-- 网关表索引
CREATE INDEX idx_ap_mac ON access_points(mac_address);
CREATE INDEX idx_ap_status ON access_points(status);

-- 模板表索引
CREATE INDEX idx_templates_type ON templates(template_type);
CREATE INDEX idx_templates_status ON templates(status);
```

## 数据完整性约束

1. **设备MAC地址唯一性**: 确保每个设备有唯一的MAC地址
2. **网关容量限制**: 通过触发器确保连接到网关的设备数不超过最大限制
3. **模板设计数据验证**: 确保模板的design_data字段包含有效的JSON数据
4. **设备绑定一致性**: 确保设备绑定的类型与关联的实体匹配

## 性能优化建议

1. **分区策略**: 对于大量历史数据的表（如device_bindings），可以按时间分区
2. **缓存策略**: 模板数据和设备状态信息适合使用Redis缓存
3. **读写分离**: 设备状态查询频繁，可以考虑读写分离
4. **批量操作**: 多设备绑定操作应该使用批量插入优化性能

## API功能详细分析

### Authentication模块
- **POST /auth/login**: 用户登录获取访问令牌
  - 输入：username, password
  - 输出：accessToken (JWT)
  - 数据库操作：验证用户凭据，生成并存储访问令牌

### DEVICE模块
- **POST /device/bind-multiple**: 批量绑定标签到人员和模板
- **POST /device/bind**: 绑定单个标签数据，使用模板数据刷新桌牌
- **POST /device/flash**: 蓝牙价格标签闪烁（桌牌不支持）
- **GET /device/{id}**: 查询单个桌牌信息
- **GET /devices**: 查询所有桌牌，支持过滤
- **POST /device/update**: 更新桌牌信息

### MEETING模块
- **GET /meeting/rooms**: 查询会议室列表
- **POST /meeting/room**: 创建会议室
- **POST /meeting/staff/batch**: 批量创建会议人员
- **POST /meeting/staff**: 创建单个会议人员
- **GET /meeting/staff**: 查询人员列表
- **POST /meeting/staff/batch-update**: 批量更新会议人员
- **POST /meeting/staff/update**: 更新单个会议人员
- **GET /meeting/staff/delete**: 删除会议人员
- **POST /meeting/room/update**: 更新会议室
- **GET /meeting/room/delete**: 删除会议室

### TEMPLATE模块
- **GET /templates**: 查询模板列表
- **POST /template**: 创建模板
- **DELETE /template**: 删除模板
- **PUT /template**: 更新模板
- **GET /template/design**: 获取模板设计数据

### AP模块
- **GET /ap/list**: 获取网关列表
- **POST /ap**: 创建网关
- **GET /ap/{mac}**: 根据MAC地址获取网关信息
- **PUT /ap**: 修改网关信息
- **DELETE /ap**: 删除网关

## 数据流分析

### 典型业务流程
1. **设备初始化流程**:
   ```
   创建网关 → 设备连接网关 → 设备注册 → 设备状态监控
   ```

2. **桌牌绑定流程**:
   ```
   创建会议室 → 创建人员 → 选择模板 → 绑定设备 → 刷新显示
   ```

3. **模板管理流程**:
   ```
   设计模板 → 保存模板 → 预览效果 → 应用到设备
   ```

### 数据一致性要求
1. 设备绑定时必须确保人员、模板、会议室都存在
2. 删除会议室前需要检查是否有关联的人员和设备
3. 模板删除前需要检查是否有设备正在使用
4. 网关删除前需要断开所有连接的设备

## 扩展建议

### 功能扩展
1. **设备分组管理**: 支持按楼层、部门等维度分组
2. **定时任务**: 支持定时更新桌牌内容
3. **消息推送**: 支持实时消息推送到桌牌
4. **数据统计**: 设备使用情况、在线率等统计

### 技术扩展
1. **多租户支持**: 添加租户隔离机制
2. **审计日志**: 记录所有操作的审计日志
3. **版本控制**: 模板和配置的版本管理
4. **备份恢复**: 数据备份和灾难恢复机制

这个数据库设计完整支持了蓝牙桌牌系统的所有API功能，包括设备管理、会议管理、模板管理和网关管理，同时考虑了系统的扩展性、性能和数据一致性需求。
